#!/usr/bin/env python3
"""
替换原始文件的脚本
"""

import shutil
import os

def main():
    original_file = 'output.json'
    updated_file = 'output_with_icons.json'
    backup_file = 'output_backup.json'
    
    try:
        # 创建备份
        print(f"📁 创建备份文件: {backup_file}")
        shutil.copy2(original_file, backup_file)
        
        # 替换原始文件
        print(f"🔄 替换原始文件: {original_file}")
        shutil.copy2(updated_file, original_file)
        
        print("✅ 成功完成文件替换!")
        print(f"   - 原始文件已备份为: {backup_file}")
        print(f"   - {original_file} 现在包含精美的AI模型图标")
        
        # 显示文件大小信息
        original_size = os.path.getsize(original_file)
        backup_size = os.path.getsize(backup_file)
        
        print(f"\n📊 文件信息:")
        print(f"   - 更新后文件大小: {original_size:,} 字节")
        print(f"   - 备份文件大小: {backup_size:,} 字节")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
