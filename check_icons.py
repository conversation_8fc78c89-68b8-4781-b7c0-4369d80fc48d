#!/usr/bin/env python3
"""
检查哪些图标有彩色版本的脚本
"""

import requests
import time

# 需要检查的图标列表 - 使用正确的文件名
ICONS_TO_CHECK = [
    "openai",
    "open-ai",
    "google",
    "gemini",
    "claude",
    "anthropic",
    "deepseek",
    "deep-seek",
    "qwen",
    "meta",
    "grok",
    "xai",
    "flux",
    "stability",
    "mistral",
    "cohere",
    "huggingface",
    "hugging-face",
    "together",
    "replicate",
    "perplexity",
    "dalle",
    "dall-e"
]

BASE_URL = "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark"

def check_icon_exists(icon_name, has_color=False):
    """检查图标是否存在"""
    suffix = "-color.png" if has_color else ".png"
    url = f"{BASE_URL}/{icon_name}{suffix}"
    
    try:
        response = requests.head(url, timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    print("🔍 检查图标可用性...")
    print("=" * 60)
    
    available_icons = {}
    
    for icon in ICONS_TO_CHECK:
        print(f"检查 {icon}...")
        
        # 检查普通版本
        normal_exists = check_icon_exists(icon, False)
        
        # 检查彩色版本
        color_exists = check_icon_exists(icon, True)
        
        available_icons[icon] = {
            'normal': normal_exists,
            'color': color_exists
        }
        
        status = []
        if normal_exists:
            status.append("✅ 普通")
        if color_exists:
            status.append("🎨 彩色")
        
        if not status:
            status.append("❌ 不存在")
            
        print(f"  {icon}: {' | '.join(status)}")
        
        # 避免请求过快
        time.sleep(0.2)
    
    print("\n" + "=" * 60)
    print("📊 总结:")
    
    # 有彩色版本的图标
    color_icons = [icon for icon, status in available_icons.items() if status['color']]
    print(f"\n🎨 有彩色版本的图标 ({len(color_icons)} 个):")
    for icon in color_icons:
        print(f"  - {icon}")
    
    # 只有普通版本的图标
    normal_only = [icon for icon, status in available_icons.items() if status['normal'] and not status['color']]
    print(f"\n⚪ 只有普通版本的图标 ({len(normal_only)} 个):")
    for icon in normal_only:
        print(f"  - {icon}")
    
    # 不存在的图标
    missing = [icon for icon, status in available_icons.items() if not status['normal'] and not status['color']]
    if missing:
        print(f"\n❌ 不存在的图标 ({len(missing)} 个):")
        for icon in missing:
            print(f"  - {icon}")

if __name__ == "__main__":
    main()
