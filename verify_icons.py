#!/usr/bin/env python3
"""
验证图标更新结果的脚本
"""

import json
from collections import Counter
import re

def analyze_icons(json_file):
    """
    分析JSON文件中的图标使用情况
    """
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    icon_counter = Counter()
    model_examples = {}
    
    for model in data:
        if 'meta' in model and 'profile_image_url' in model['meta']:
            icon_url = model['meta']['profile_image_url']
            
            # 提取图标文件名
            if 'favicon.png' in icon_url:
                icon_name = 'favicon.png (未更新)'
            else:
                # 从URL中提取图标文件名
                match = re.search(r'/([^/]+\.png)$', icon_url)
                if match:
                    icon_name = match.group(1)
                else:
                    icon_name = 'unknown'
            
            icon_counter[icon_name] += 1
            
            # 记录每种图标的示例模型
            if icon_name not in model_examples:
                model_examples[icon_name] = []
            if len(model_examples[icon_name]) < 3:  # 只记录前3个示例
                model_examples[icon_name].append(model.get('id', 'unknown'))
    
    return icon_counter, model_examples

def main():
    print("🔍 分析图标更新结果...")
    
    # 分析更新后的文件
    icon_stats, examples = analyze_icons('output_with_icons.json')
    
    print(f"\n📊 图标使用统计:")
    print("=" * 60)
    
    total_models = sum(icon_stats.values())
    
    for icon, count in icon_stats.most_common():
        percentage = (count / total_models) * 100
        print(f"{icon:<25} {count:>3} 个模型 ({percentage:>5.1f}%)")
        
        # 显示示例模型
        if icon in examples and examples[icon]:
            example_models = examples[icon][:3]
            print(f"{'':>25} 示例: {', '.join(example_models)}")
        print()
    
    print(f"总计: {total_models} 个模型")
    
    # 检查是否还有未更新的模型
    favicon_count = icon_stats.get('favicon.png (未更新)', 0)
    if favicon_count > 0:
        print(f"\n⚠️  警告: 还有 {favicon_count} 个模型使用默认favicon图标")
    else:
        print(f"\n✅ 所有模型都已成功更新为精美图标!")

if __name__ == "__main__":
    main()
