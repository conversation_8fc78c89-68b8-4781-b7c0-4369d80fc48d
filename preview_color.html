<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型图标预览 - 彩色版本</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .icon-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon-card img {
            width: 64px;
            height: 64px;
            margin-bottom: 15px;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        .model-count {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 AI模型图标预览 - 彩色版本</h1>
        <p>已为所有AI模型配置专属品牌图标，支持彩色和普通版本</p>
    </div>

    <div class="icon-grid">
        <!-- DeepSeek - 彩色版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/deepseek-color.png" alt="DeepSeek">
            <div class="icon-name">DeepSeek</div>
            <div class="model-count">17 个模型 (彩色)</div>
        </div>

        <!-- Qwen - 彩色版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/qwen-color.png" alt="Qwen">
            <div class="icon-name">Qwen</div>
            <div class="model-count">彩色版本</div>
        </div>

        <!-- Claude - 彩色版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/claude-color.png" alt="Claude">
            <div class="icon-name">Claude</div>
            <div class="model-count">彩色版本</div>
        </div>

        <!-- Meta - 彩色版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/meta-color.png" alt="Meta">
            <div class="icon-name">Meta</div>
            <div class="model-count">彩色版本</div>
        </div>

        <!-- Mistral - 彩色版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/mistral-color.png" alt="Mistral">
            <div class="icon-name">Mistral</div>
            <div class="model-count">彩色版本</div>
        </div>

        <!-- OpenAI - 普通版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openai.png" alt="OpenAI">
            <div class="icon-name">OpenAI</div>
            <div class="model-count">普通版本</div>
        </div>

        <!-- Gemini - 普通版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/gemini.png" alt="Gemini">
            <div class="icon-name">Gemini</div>
            <div class="model-count">普通版本</div>
        </div>

        <!-- Grok - 普通版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/grok.png" alt="Grok">
            <div class="icon-name">Grok</div>
            <div class="model-count">普通版本</div>
        </div>

        <!-- Flux - 普通版本 -->
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/flux.png" alt="Flux">
            <div class="icon-name">Flux</div>
            <div class="model-count">普通版本</div>
        </div>
    </div>

    <div style="margin-top: 40px; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <h2>✅ 更新总结</h2>
        <ul>
            <li><strong>总共更新了 142 个模型的图标</strong></li>
            <li><strong>DeepSeek 模型: 17 个</strong> - 使用彩色版本</li>
            <li><strong>不区分大小写匹配</strong> - 解决了 "Deepseek" 等大小写问题</li>
            <li><strong>彩色图标</strong> - DeepSeek, Qwen, Claude, Meta, Mistral 等使用彩色版本</li>
            <li><strong>普通图标</strong> - OpenAI, Gemini, Grok, Flux 等使用普通版本</li>
        </ul>
    </div>
</body>
</html>
