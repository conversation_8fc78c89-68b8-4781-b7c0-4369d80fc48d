<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型图标预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .icon-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon-card img {
            width: 64px;
            height: 64px;
            margin-bottom: 15px;
        }
        .icon-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .model-count {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .examples {
            font-size: 12px;
            color: #888;
            line-height: 1.4;
        }
        .success {
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 AI模型图标更新完成 (彩色版)</h1>
        <p class="success">✅ 成功为 142 个AI模型匹配了精美彩色图标</p>
        <p>基于 LobeChat 图标库彩色版本，所有模型现在都有了对应的品牌图标</p>
        <p style="color: #ff6b35; font-weight: bold;">🔥 特别修复了 DeepSeek 模型的图标匹配问题！</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <h3>📊 更新统计</h3>
            <p><strong>142</strong> 个模型已更新</p>
            <p><strong>8</strong> 种不同图标</p>
            <p><strong>100%</strong> 更新成功率</p>
        </div>
        <div class="stat-card">
            <h3>🏆 主要提供商</h3>
            <p>OpenAI: 79 个模型 (55.6%)</p>
            <p style="color: #ff6b35;">🔥 DeepSeek: 17 个模型 (12.0%)</p>
            <p>Qwen: 14 个模型 (9.9%)</p>
        </div>
    </div>

    <h2>🖼️ 图标预览</h2>
    <div class="icon-grid">
        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openai.png" alt="OpenAI">
            <div class="icon-name">OpenAI</div>
            <div class="model-count">79 个模型 (55.6%)</div>
            <div class="examples">GPT-4.1, ChatGPT-4o, DALL-E-3</div>
        </div>

        <div class="icon-card" style="border: 2px solid #ff6b35;">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/deepseek.png" alt="DeepSeek">
            <div class="icon-name" style="color: #ff6b35;">🔥 DeepSeek</div>
            <div class="model-count">17 个模型 (12.0%)</div>
            <div class="examples">DeepSeek-R1, DeepSeek-V3, DeepSeek-Coder</div>
        </div>

        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/qwen.png" alt="Qwen">
            <div class="icon-name">Qwen (阿里巴巴)</div>
            <div class="model-count">14 个模型 (9.9%)</div>
            <div class="examples">Qwen3-Coder, QwQ-32B</div>
        </div>

        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/stability.png" alt="Stability AI">
            <div class="icon-name">Stability AI</div>
            <div class="model-count">10 个模型 (7.0%)</div>
            <div class="examples">Flux-1.1-Pro, Flux-Dev</div>
        </div>

        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/google.png" alt="Google">
            <div class="icon-name">Google</div>
            <div class="model-count">8 个模型 (5.6%)</div>
            <div class="examples">Gemini-2.5-Flash, Gemini-2.5-Pro</div>
        </div>

        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/xai.png" alt="xAI">
            <div class="icon-name">xAI</div>
            <div class="model-count">7 个模型 (4.9%)</div>
            <div class="examples">Grok-Flash, Grok-Flash-Thinking</div>
        </div>

        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/anthropic.png" alt="Anthropic">
            <div class="icon-name">Anthropic</div>
            <div class="model-count">4 个模型 (2.8%)</div>
            <div class="examples">Claude-3.5-Sonnet, Claude-3-Haiku</div>
        </div>

        <div class="icon-card">
            <img src="https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/meta.png" alt="Meta">
            <div class="icon-name">Meta</div>
            <div class="model-count">3 个模型 (2.1%)</div>
            <div class="examples">Llama-3.3-70B, Llama-4-Maverick</div>
        </div>
    </div>

    <div class="header" style="margin-top: 40px;">
        <h3>🎯 更新说明</h3>
        <p>所有原本使用 <code>/static/favicon.png</code> 的模型现在都有了对应的精美品牌图标</p>
        <p>图标来源于 LobeChat 的开源图标库，确保高质量和一致性</p>
        <p>文件已保存为 <code>output_with_icons.json</code></p>
    </div>
</body>
</html>
