#!/usr/bin/env python3
"""
AI模型图标更新脚本
基于LobeChat图标库为JSON文件中的AI模型匹配精美图标
"""

import json
import re
from typing import Dict, List, Any

# LobeChat图标库CDN基础URL
ICON_BASE_URL = "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light"

# AI模型到图标的映射表
MODEL_ICON_MAPPING = {
    # OpenAI 系列
    "openai": "openai.png",
    "gpt": "openai.png",
    "chatgpt": "openai.png",
    "dall-e": "openai.png",
    "davinci": "openai.png",
    "babbage": "openai.png",
    "code-davinci": "openai.png",

    # Google 系列
    "gemini": "google.png",
    "google": "google.png",
    "vertex": "google.png",
    "vertexai": "google.png",

    # Anthropic Claude 系列
    "claude": "anthropic.png",
    "anthropic": "anthropic.png",

    # DeepSeek 系列
    "deepseek": "deepseek.png",

    # Qwen/阿里巴巴 系列
    "qwen": "qwen.png",
    "alibaba": "qwen.png",
    "qwq": "qwen.png",

    # Meta/Facebook Llama 系列
    "llama": "meta.png",
    "meta": "meta.png",
    "facebook": "meta.png",

    # xAI Grok 系列
    "grok": "xai.png",
    "xai": "xai.png",
    "酒馆": "xai.png",  # 酒馆-Flash 等模型

    # Stability AI / Flux 图像生成
    "flux": "stability.png",
    "black-forest": "stability.png",
    "stability": "stability.png",

    # Mistral AI
    "mistral": "mistral.png",

    # Cohere
    "cohere": "cohere.png",

    # Hugging Face
    "huggingface": "huggingface.png",
    "hf": "huggingface.png",

    # Together AI
    "together": "together.png",

    # Replicate
    "replicate": "replicate.png",

    # Perplexity
    "perplexity": "perplexity.png",

    # 其他模型
    "clip": "openai.png",  # CLIP是OpenAI的模型
    "laion": "openai.png",

    # 错误状态
    "error": "openai.png",  # 默认使用OpenAI图标
}

def extract_model_provider(model_id: str, model_name: str = "") -> str:
    """
    从模型ID或名称中提取提供商信息
    """
    # 转换为小写便于匹配
    id_lower = model_id.lower()
    name_lower = model_name.lower() if model_name else ""
    
    # 检查各种模式
    patterns = [
        # 直接匹配提供商名称
        (r'openai|gpt|chatgpt|dall-e|davinci|babbage|code-davinci', 'openai'),
        (r'gemini|google|vertex', 'gemini'),
        (r'claude|anthropic', 'claude'),
        (r'deepseek', 'deepseek'),
        (r'qwen|alibaba|qwq', 'qwen'),
        (r'llama|meta|facebook', 'llama'),
        (r'grok|xai|酒馆', 'grok'),
        (r'flux|black-forest|stability', 'flux'),
        (r'mistral', 'mistral'),
        (r'cohere', 'cohere'),
        (r'huggingface|hf', 'huggingface'),
        (r'together', 'together'),
        (r'replicate', 'replicate'),
        (r'perplexity', 'perplexity'),
        (r'clip|laion', 'clip'),
    ]
    
    # 检查模型ID
    for pattern, provider in patterns:
        if re.search(pattern, id_lower):
            return provider
    
    # 检查模型名称
    for pattern, provider in patterns:
        if re.search(pattern, name_lower):
            return provider
    
    # 默认返回openai
    return 'openai'

def get_icon_url(provider: str) -> str:
    """
    根据提供商获取图标URL
    """
    icon_file = MODEL_ICON_MAPPING.get(provider, "openai.png")
    return f"{ICON_BASE_URL}/{icon_file}"

def update_model_icons(json_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    更新JSON数据中的模型图标
    """
    updated_count = 0
    
    for model in json_data:
        if 'meta' in model and 'profile_image_url' in model['meta']:
            # 只更新使用默认favicon的模型
            if model['meta']['profile_image_url'] == '/static/favicon.png':
                model_id = model.get('id', '')
                model_name = model.get('name', '')
                
                # 提取提供商信息
                provider = extract_model_provider(model_id, model_name)
                
                # 更新图标URL
                new_icon_url = get_icon_url(provider)
                model['meta']['profile_image_url'] = new_icon_url
                
                updated_count += 1
                print(f"更新模型: {model_id} -> {provider} -> {new_icon_url}")
    
    print(f"\n总共更新了 {updated_count} 个模型的图标")
    return json_data

def main():
    """
    主函数
    """
    input_file = 'output.json'
    output_file = 'output_with_icons.json'
    
    try:
        # 读取JSON文件
        print(f"正在读取文件: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"成功读取 {len(data)} 个模型")
        
        # 更新图标
        print("\n开始更新模型图标...")
        updated_data = update_model_icons(data)
        
        # 保存更新后的文件
        print(f"\n正在保存到文件: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(updated_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 成功保存更新后的文件: {output_file}")
        
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {input_file}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
