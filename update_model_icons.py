#!/usr/bin/env python3
"""
AI模型图标更新脚本
基于LobeChat图标库为JSON文件中的AI模型匹配精美图标
"""

import json
import re
from typing import Dict, List, Any

# LobeChat图标库CDN基础URL - 使用彩色图标
ICON_BASE_URL = "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark"

# AI模型到图标的映射表 - 使用彩色版本
MODEL_ICON_MAPPING = {
    # OpenAI 系列
    "openai": "openai-color.png",
    "gpt": "openai-color.png",
    "chatgpt": "openai-color.png",
    "dall-e": "dalle-color.png",  # DALL-E有专门的彩色图标
    "davinci": "openai-color.png",
    "babbage": "openai-color.png",
    "code-davinci": "openai-color.png",

    # Google 系列
    "gemini": "gemini-color.png",
    "google": "google-color.png",
    "vertex": "google-color.png",
    "vertexai": "google-color.png",

    # Anthropic Claude 系列
    "claude": "claude-color.png",
    "anthropic": "claude-color.png",

    # DeepSeek 系列
    "deepseek": "deepseek-color.png",

    # Qwen/阿里巴巴 系列
    "qwen": "qwen-color.png",
    "alibaba": "qwen-color.png",
    "qwq": "qwen-color.png",

    # Meta/Facebook Llama 系列
    "llama": "meta-color.png",
    "meta": "meta-color.png",
    "facebook": "meta-color.png",

    # xAI Grok 系列
    "grok": "grok-color.png",
    "xai": "grok-color.png",
    "酒馆": "grok-color.png",  # 酒馆-Flash 等模型

    # Stability AI / Flux 图像生成
    "flux": "flux-color.png",
    "black-forest": "flux-color.png",
    "stability": "stability-color.png",

    # Mistral AI
    "mistral": "mistral-color.png",

    # Cohere
    "cohere": "cohere-color.png",

    # Hugging Face
    "huggingface": "huggingface-color.png",
    "hf": "huggingface-color.png",

    # Together AI
    "together": "together-color.png",

    # Replicate
    "replicate": "replicate-color.png",

    # Perplexity
    "perplexity": "perplexity-color.png",

    # 其他模型
    "clip": "openai-color.png",  # CLIP是OpenAI的模型
    "laion": "openai-color.png",

    # 错误状态
    "error": "openai-color.png",  # 默认使用OpenAI图标
}

def extract_model_provider(model_id: str, model_name: str = "", debug: bool = False) -> str:
    """
    从模型ID或名称中提取提供商信息
    """
    # 转换为小写便于匹配，并合并ID和名称进行搜索
    search_text = f"{model_id} {model_name}".lower()
    id_lower = model_id.lower()
    name_lower = model_name.lower() if model_name else ""

    if debug:
        print(f"  调试: 分析模型 '{model_id}' (name: '{model_name}')")
        print(f"    搜索文本: '{search_text}'")
    
    # 检查各种模式 - 更精确的匹配
    patterns = [
        # DeepSeek 优先匹配（包含更多变体）
        (r'deepseek|deep-seek', 'deepseek'),
        # OpenAI 系列
        (r'openai|gpt|chatgpt|dall-e|davinci|babbage|code-davinci', 'openai'),
        # Google 系列
        (r'gemini|google|vertex', 'gemini'),
        # Anthropic Claude 系列
        (r'claude|anthropic', 'claude'),
        # Qwen/阿里巴巴 系列
        (r'qwen|alibaba|qwq', 'qwen'),
        # Meta/Facebook Llama 系列
        (r'llama|meta|facebook', 'llama'),
        # xAI Grok 系列
        (r'grok|xai|酒馆', 'grok'),
        # Stability AI / Flux 图像生成
        (r'flux|black-forest|stability', 'flux'),
        # 其他提供商
        (r'mistral', 'mistral'),
        (r'cohere', 'cohere'),
        (r'huggingface|hf', 'huggingface'),
        (r'together', 'together'),
        (r'replicate', 'replicate'),
        (r'perplexity', 'perplexity'),
        (r'clip|laion', 'clip'),
    ]
    
    # 首先检查合并的搜索文本（更准确）
    for pattern, provider in patterns:
        if re.search(pattern, search_text):
            if debug:
                print(f"    匹配到搜索文本模式 '{pattern}' -> {provider}")
            return provider

    # 然后检查模型ID
    for pattern, provider in patterns:
        if re.search(pattern, id_lower):
            if debug:
                print(f"    匹配到ID模式 '{pattern}' -> {provider}")
            return provider

    # 最后检查模型名称
    for pattern, provider in patterns:
        if re.search(pattern, name_lower):
            if debug:
                print(f"    匹配到名称模式 '{pattern}' -> {provider}")
            return provider

    # 默认返回openai
    if debug:
        print(f"    未匹配到任何模式，使用默认: openai")
    return 'openai'

def get_icon_url(provider: str) -> str:
    """
    根据提供商获取图标URL
    """
    icon_file = MODEL_ICON_MAPPING.get(provider, "openai.png")
    return f"{ICON_BASE_URL}/{icon_file}"

def update_model_icons(json_data: List[Dict[str, Any]], debug: bool = False) -> List[Dict[str, Any]]:
    """
    更新JSON数据中的模型图标
    """
    updated_count = 0
    deepseek_count = 0

    for model in json_data:
        if 'meta' in model and 'profile_image_url' in model['meta']:
            # 只更新使用默认favicon的模型
            if model['meta']['profile_image_url'] == '/static/favicon.png':
                model_id = model.get('id', '')
                model_name = model.get('name', '')

                # 检查是否是DeepSeek模型
                is_deepseek = 'deepseek' in model_id.lower() or 'deepseek' in model_name.lower()
                if is_deepseek:
                    deepseek_count += 1

                # 提取提供商信息
                provider = extract_model_provider(model_id, model_name, debug=debug and is_deepseek)

                # 更新图标URL
                new_icon_url = get_icon_url(provider)
                model['meta']['profile_image_url'] = new_icon_url

                updated_count += 1
                status = "🔥 DeepSeek" if is_deepseek else "✅"
                print(f"{status} 更新模型: {model_id} -> {provider} -> {new_icon_url}")

    print(f"\n📊 总共更新了 {updated_count} 个模型的图标")
    print(f"🔥 其中 DeepSeek 模型: {deepseek_count} 个")
    return json_data

def main():
    """
    主函数
    """
    input_file = 'output.json'
    output_file = 'output_with_icons.json'
    
    try:
        # 读取JSON文件
        print(f"正在读取文件: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"成功读取 {len(data)} 个模型")
        
        # 更新图标
        print("\n开始更新模型图标...")
        updated_data = update_model_icons(data, debug=True)
        
        # 保存更新后的文件
        print(f"\n正在保存到文件: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(updated_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 成功保存更新后的文件: {output_file}")
        
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {input_file}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
