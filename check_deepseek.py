#!/usr/bin/env python3
"""
检查DeepSeek模型的脚本
"""

import json
import re

def check_deepseek_models():
    with open('output.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    deepseek_models = []
    favicon_models = []
    
    for model in data:
        model_id = model.get('id', '').lower()
        model_name = model.get('name', '').lower()
        
        # 检查是否包含deepseek
        if 'deepseek' in model_id or 'deepseek' in model_name:
            profile_url = model.get('meta', {}).get('profile_image_url', '')
            deepseek_models.append({
                'id': model.get('id', ''),
                'name': model.get('name', ''),
                'icon': profile_url
            })
        
        # 检查还有多少模型使用favicon
        if model.get('meta', {}).get('profile_image_url', '') == '/static/favicon.png':
            favicon_models.append(model.get('id', ''))
    
    print(f"🔍 找到 {len(deepseek_models)} 个DeepSeek相关模型:")
    for model in deepseek_models:
        icon_status = "✅ 已更新" if "favicon.png" not in model['icon'] else "❌ 未更新"
        print(f"  {model['id']} - {icon_status}")
        print(f"    图标: {model['icon']}")
    
    print(f"\n📊 还有 {len(favicon_models)} 个模型使用默认favicon")
    if len(favicon_models) > 0:
        print("前10个未更新的模型:")
        for model_id in favicon_models[:10]:
            print(f"  - {model_id}")

if __name__ == "__main__":
    check_deepseek_models()
